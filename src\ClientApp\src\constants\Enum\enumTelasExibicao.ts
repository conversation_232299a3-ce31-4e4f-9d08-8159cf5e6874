export type Option = {
  label: string;
  value: number;
};

export type EnumTelasProps = {
  label: string;
  options: Option[];
};

export const enumTelasExibicao: EnumTelasProps[] = [
  {
    options: [{ label: 'Dashboard', value: 1 }],
    label: 'Inicial',
  },
  {
    options: [
      { label: 'Clientes', value: 2 },
      { label: 'Fornecedores', value: 3 },
      { label: 'Vendedores', value: 4 },
      { label: 'Transportadoras', value: 5 },
      { label: 'Produtos', value: 6 },
    ],
    label: 'Cadastros',
  },
  {
    options: [
      { label: 'PDV', value: 7 },
      { label: 'Operações', value: 8 },
      { label: 'Vouchers', value: 9 },
      { label: 'Metas e Comissões', value: 10 },
      { label: 'SmartPOS', value: 11 },
    ],
    label: 'Vendas',
  },
  {
    label: 'Estoque',
    options: [
      { label: 'Entrada de Mercadoria', value: 12 },
      { label: 'Movimentação de Estoque', value: 13 },
      { label: 'Transferência de Estoque', value: 14 },
      { label: 'Conferência de Estoque', value: 15 },
      { label: 'Histórico de Produto', value: 16 },
    ],
  },
  {
    options: [
      { label: 'Contas a Receber', value: 17 },
      { label: 'Contas a Pagar', value: 18 },
      { label: 'Recebimentos de Contas', value: 19 },
      { label: 'Conciliação de Contas', value: 20 },
      { label: 'Extrato', value: 21 },
      { label: 'Controle de Caixa', value: 22 },
      { label: 'Faturas', value: 23 },
    ],
    label: 'Financeiro',
  },
  {
    options: [
      { label: 'Nota Fiscais', value: 24 },
      { label: 'Exportar XML', value: 25 },
      { label: 'Manifestação de Destinatário', value: 26 },
    ],
    label: 'Fiscal',
  },
  {
    label: 'Relatórios Produtos',
    options: [
      { label: 'Produtos vendidos', value: 27 },
      { label: 'Produtos com preço', value: 28 },
      { label: 'Catálogo de produtos', value: 29 },
      { label: 'Personalizados', value: 30 },
      { label: 'Inventário', value: 31 },
      { label: 'Estoque', value: 32 },
    ],
  },
  {
    label: 'Relatórios Clientes',
    options: [
      { label: 'Cadastro Completo', value: 33 },
      { label: 'Personalizados', value: 34 },
      { label: 'Informações dos clientes', value: 35 },
      { label: 'Clientes por Segmento', value: 36 },
    ],
  },
  {
    label: 'Relatórios Vendas',
    options: [
      { label: 'Vendas', value: 37 },
      { label: 'Curva ABC', value: 38 },
    ],
  },
  {
    label: 'Relatórios Operações',
    options: [{ label: 'Consignação', value: 39 }],
  },
  {
    label: 'Relatórios Compras',
    options: [{ label: 'Produtos por compras', value: 40 }],
  },
  {
    label: 'Integrações',
    options: [
      { label: 'Loja de aplicativos', value: 41 },
      { label: 'Dashboard Gerencial', value: 42 },
      { label: 'Smart POS', value: 43 },
      { label: 'Tray', value: 44 },
      { label: 'Zoop Recebimentos', value: 45 },
      { label: 'PDV Offline', value: 46 },
      { label: 'Frente de Caixa', value: 47 },
    ],
  },
  {
    label: 'Configurações',
    options: [
      { label: 'Minhas lojas', value: 48 },
      { label: 'Multas e Juros', value: 49 },
      { label: 'Usuários', value: 50 },
      { label: 'Perfil de usuários', value: 51 },
      { label: 'Histórico de ações', value: 52 },
      { label: 'Categoria de clientes', value: 53 },
      { label: 'Importar clientes', value: 54 },
      { label: 'Relatórios personalizados', value: 55 },
      { label: 'Importar fornecedores', value: 56 },
      { label: 'Categorias de produtos', value: 57 },
      { label: 'Tabela de preços', value: 58 },
      { label: 'Promoção', value: 59 },
      { label: 'Unidades de medida', value: 60 },
      { label: 'Marcas', value: 61 },
      { label: 'Cores', value: 62 },
      { label: 'Tamanhos', value: 63 },
      { label: 'Local de estoque', value: 64 },
      { label: 'Etiquetas personalizadas', value: 65 },
      { label: 'Relatórios personalizados produtos', value: 66 },
      { label: 'Importar produtos', value: 67 },
      { label: 'Contas financeiras', value: 68 },
      { label: 'Plano de contas', value: 69 },
      { label: 'Formas de pagamento', value: 70 },
      { label: 'Formas de recebimento', value: 71 },
      { label: 'Credenciadora de cartão', value: 72 },
      { label: 'Regra fiscal', value: 73 },
      { label: 'Campos personalizados', value: 74 },
      { label: 'Padronização', value: 75 },
    ],
  },
  {
    label: 'Outros',
    options: [{ label: 'Ajuda', value: 76 }],
  },
];
