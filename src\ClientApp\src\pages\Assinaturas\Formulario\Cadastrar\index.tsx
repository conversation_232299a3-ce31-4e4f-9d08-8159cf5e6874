import { useCallback, useEffect, useState } from 'react';
import { FormProvider, useForm } from 'react-hook-form';
import { useNavigate } from 'react-router-dom';
import { toast } from 'react-toastify';

import { ConstantEnderecoWebservice } from 'constants/constantEnderecoWebservice';
import { ConstanteRotas } from 'constants/constantRotas';
import api, { ResponseApi } from 'services/api';
import { useAssinaturasContext } from 'store/Assinaturas/AssinaturaContext';

import { LayoutFormPage } from 'components/Layout/FormPage';

import { FormAssinaturas } from '..';
import { dadosApiAssinatura } from '../dataApi';
import {
  FormData,
  yupResolverCadastroLoja,
  yupResolverCadastroCompleto,
} from '../validationForms';
import { useAssinaturaFormulario } from '../hook';

export const CadastrarAssinaturas = () => {
  const [isLoading, setIsLoading] = useState(false);

  const { formDefaultValues } = useAssinaturaFormulario();

  const {
    isCriarNovaLoja,
    listDataResponsavel,
    contaCliente,
    listServicosAdicionais,
    setListServicosAdicionais,
    isPlanoAdicionado,
  } = useAssinaturasContext();

  const formMethods = useForm<FormData>({
    resolver: isCriarNovaLoja
      ? yupResolverCadastroLoja
      : yupResolverCadastroCompleto,
    defaultValues: {
      ...formDefaultValues,
      diaVencimento: formDefaultValues.diaVencimento,
      dataExpiracao: formDefaultValues.dataExpiracao,
    },
  });

  const { reset, handleSubmit: onSubmit } = formMethods;

  const navigate = useNavigate();

  const getResponseCadastroCliente = useCallback(
    async (data: FormData) => {
      const propsAssinaturaCadastrar = dadosApiAssinatura({
        data,
        servicos: listServicosAdicionais,
        naoExibirResponsavel: false,
      });

      const response = await api.post<void, ResponseApi<FormData>>(
        ConstantEnderecoWebservice.CADASTRAR_ASSINATURA,
        {
          ...propsAssinaturaCadastrar,
        }
      );

      return response;
    },
    [dadosApiAssinatura, listServicosAdicionais]
  );

  const getResponseCadastroNovaLoja = useCallback(
    async (data: FormData) => {
      const propsAssinaturaCadastrar = dadosApiAssinatura({
        data,
        servicos: listServicosAdicionais,
        naoExibirResponsavel: true,
      });

      const response = await api.post<void, ResponseApi<FormData>>(
        `${ConstantEnderecoWebservice.CADASTRAR_ASSINATURA_LOJA}?contaClienteId=${contaCliente}&idAssinaturaBasePrecos=${data.idAssinaturaBasePrecos}`,
        {
          ...propsAssinaturaCadastrar,
        }
      );

      return response;
    },
    [dadosApiAssinatura, listServicosAdicionais]
  );

  const handleSubmit = onSubmit(async (data: FormData) => {
    setIsLoading(true);
    let response;

    if (isCriarNovaLoja) {
      response = await getResponseCadastroNovaLoja(data);
    } else {
      response = await getResponseCadastroCliente(data);
    }

    if (response && response.sucesso) {
      toast.success('Cadastro realizado com sucesso.');
      navigate(ConstanteRotas.ASSINATURAS);
    }
    setIsLoading(false);
  });

  const handleSubmitReset = onSubmit(async (data) => {
    setIsLoading(true);
    const response = await getResponseCadastroCliente(data);

    if (response.sucesso) {
      reset({
        ...formDefaultValues,
        diaVencimento: formDefaultValues.diaVencimento,
      });
      setListServicosAdicionais([]);
      toast.success('Cadastro realizado com sucesso.');
    }
    setIsLoading(false);
  });

  useEffect(() => {
    if (isCriarNovaLoja) {
      reset({
        ...listDataResponsavel,
        dominio: listDataResponsavel.dominio?.split('.')[0],
        telefoneResponsavel: listDataResponsavel.celular,
      });
    }
  }, [isCriarNovaLoja, listDataResponsavel, reset]);

  return (
    <LayoutFormPage
      onSubmit={handleSubmit}
      onResetSubmit={!isCriarNovaLoja ? handleSubmitReset : undefined}
      isLoading={isLoading}
      isDisabledReset={!isPlanoAdicionado}
      isDisabledSubmit={!isPlanoAdicionado}
    >
      <FormProvider {...formMethods}>
        <FormAssinaturas isCadastrar />
      </FormProvider>
    </LayoutFormPage>
  );
};
