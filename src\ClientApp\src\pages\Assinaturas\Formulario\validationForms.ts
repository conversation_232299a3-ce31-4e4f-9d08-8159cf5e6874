import { yupResolver as yupResolverInstance } from '@hookform/resolvers/yup';
import * as yup from 'yup';

import { EnumValidacoesSistema } from 'constants/Enum/enumValidacoesSistema';
import {
  DominiosDeliveryEnum,
  optionsFomerZendar,
  optionsPowerStockChef,
} from 'constants/Enum/enumDominios';
import auth from 'modules/auth';
import { ResponsavelProps } from 'store/Assinaturas/AssinaturaContext';

import { SelectOptions } from 'components/Select/SelectDefault';
import { CepValue } from 'components/Input/InputChakra/InputCep';

export type ServicosAdicionais = {
  servicoId: string;
  tipo: number;
  quantidade?: number | string;
  valorRepasse: number;
  valorTotalRepasse: number;
  descricaoAdicional: string;
  valorUnitarioRepasse: number;
  servicoNome: string;
  proximoVencimento?: Date;
};

export type ServicoAdicionalProps = ServicosAdicionais & {
  serviceIndex?: number;
};

export type FormData = {
  nome: string;
  cidadeNome: string | SelectOptions | null;
  telefoneResponsavel: string;
  email: string;
  dominio: string;
  urlDominio: string;
  dominioDelivery: string;
  urlDominioDelivery: any;
  usuario: string;
  cnpj: string;
  razaoSocial: string;
  fantasia: string;
  inscricaoEstadual?: string;
  inscricaoMunicipal?: string;
  codigoExterno: string;
  observacao: string;
  cep: string | CepValue;
  logradouro: string;
  numero: string;
  bairro: string;
  celular: string;
  telefone: string;
  emailContato: string;
  tabelaPrecoId: string | null;
  paisNome: string | number | SelectOptions | null;
  complemento: string;
  contato: string;
  idAssinaturaBasePrecos?: string | null;
  bancoDados: string;
  estado?: SelectOptions;
  codigoERP?: number;
  diaVencimento: string;
  dataExpiracao: string | Date;
  dataUltimaValidacao?: string | Date;
  validacaoAutomatica: boolean;
};

export type LojaProps = {
  cnpj: string;
  razaoSocial: string;
  fantasia: string;
  inscricaoEstadual?: string;
  inscricaoMunicipal?: string;
  cep?: string;
  logradouro: string;
  numero: string;
  complemento: string;
  bairro: string;
  cidadeId: number;
  paisId: number;
  telefone?: string;
  celular?: string;
  emailContato: string;
  contato: string;
  cidadeNome: string | SelectOptions;
  paisNome: string | SelectOptions;
  codigoERP?: number;
  diaVencimento: number;
  dataExpiracao: Date;
  validacaoAutomatica: boolean;
};

export type ServicoProps = {
  servicoId: string;
  servicoNome: string;
  tipo: number;
  quantidade: number;
  valor: number;
  proximoVencimento: string;
  valorRepasse: number;
  descricaoAdicional: string;
};

export type AssinaturaProps = {
  id: string;
  responsavel: ResponsavelProps;
  loja: LojaProps;
  diaVencimento: number;
  dataExpiracao: Date;
  dataUltimaValidacao: Date;
  validacaoAutomatica: boolean;
  codigoExterno: string;
  dataCancelamento: Date | null;
  motivoCancelamento: string | null;
  tabelaPrecoId: string;
  servicos: ServicosAdicionais[];
  servicosContratados: ServicosAdicionais[];
  servicosExcluidos?: string[];
  observacao: string;
};

const schemaDadosLoja = {
  cnpj: yup.string().required(EnumValidacoesSistema.CAMPO_OBRIGATORIO),
  razaoSocial: yup.string().required(EnumValidacoesSistema.CAMPO_OBRIGATORIO),
  nomeFantasia: yup.string(),
  inscricaoEstadual: yup.mixed(),
  inscricaoMunicipal: yup.mixed(),
  cep: yup.lazy((newValue) => {
    if (newValue?.isGetCep) {
      return yup
        .object({
          value: yup.string().required(EnumValidacoesSistema.CAMPO_OBRIGATORIO),
        })
        .required(EnumValidacoesSistema.CAMPO_OBRIGATORIO);
    } else {
      return yup.string().required(EnumValidacoesSistema.CAMPO_OBRIGATORIO);
    }
  }),

  logradouro: yup.string().required(EnumValidacoesSistema.CAMPO_OBRIGATORIO),
  numero: yup.string().required(EnumValidacoesSistema.CAMPO_OBRIGATORIO),
  bairro: yup.string().required(EnumValidacoesSistema.CAMPO_OBRIGATORIO),
  celular: yup
    .string()
    .nullable()
    .required(EnumValidacoesSistema.CAMPO_OBRIGATORIO),
  telefone: yup
    .string()
    .nullable()
    .required(EnumValidacoesSistema.CAMPO_OBRIGATORIO),
  emailContato: yup
    .string()
    .required(EnumValidacoesSistema.CAMPO_OBRIGATORIO)
    .email(EnumValidacoesSistema.EMAIL_INVALIDO),
  contato: yup.string().required(EnumValidacoesSistema.CAMPO_OBRIGATORIO),
  codigoExterno: yup.string(),

  diaVencimento: yup.string().required(EnumValidacoesSistema.CAMPO_OBRIGATORIO),
  dataExpiracao: yup.string().required(EnumValidacoesSistema.CAMPO_OBRIGATORIO),
};

const schemaDadosResponsavel = {
  nome: yup.string().required(EnumValidacoesSistema.CAMPO_OBRIGATORIO),
  telefoneResponsavel: yup
    .string()
    .required(EnumValidacoesSistema.CAMPO_OBRIGATORIO),
  email: yup
    .string()
    .required(EnumValidacoesSistema.CAMPO_OBRIGATORIO)
    .email(EnumValidacoesSistema.EMAIL_INVALIDO),
  dominio: yup.string().required(EnumValidacoesSistema.CAMPO_OBRIGATORIO),
};

const schemaDadosAlterarResponsavel = {
  nome: yup.string().required(EnumValidacoesSistema.CAMPO_OBRIGATORIO),
  telefoneResponsavel: yup
    .string()
    .required(EnumValidacoesSistema.CAMPO_OBRIGATORIO),
  email: yup
    .string()
    .required(EnumValidacoesSistema.CAMPO_OBRIGATORIO)
    .email(EnumValidacoesSistema.EMAIL_INVALIDO),
  dominio: yup.string().required(EnumValidacoesSistema.CAMPO_OBRIGATORIO),
};

const schemaCadastroCompleto = yup.object().shape({
  ...schemaDadosResponsavel,
  ...schemaDadosLoja,
  usuario: yup.string().required(EnumValidacoesSistema.CAMPO_OBRIGATORIO),
});

const schemaCadastroLoja = yup.object().shape({
  ...schemaDadosLoja,
  idAssinaturaBasePrecos: yup
    .string()
    .nullable()
    .required(EnumValidacoesSistema.CAMPO_OBRIGATORIO),
});

const schemaAlterarLoja = yup.object().shape({
  ...schemaDadosLoja,
});

const schemaAlterarResponsavel = yup.object().shape({
  ...schemaDadosAlterarResponsavel,
});

export const yupResolverCadastroCompleto = yupResolverInstance(
  schemaCadastroCompleto
);
export const yupResolverCadastroLoja = yupResolverInstance(schemaCadastroLoja);
export const yupResolverAlterar = yupResolverInstance(schemaAlterarResponsavel);
export const yupResolverLojaAlterar = yupResolverInstance(schemaAlterarLoja);
export const formDefaultValues = {
  validacaoAutomatica: true,
};

// Adicione validacaoAutomatica ao schema
const schema = yup.object().shape({
  validacaoAutomatica: yup.boolean(),
});
