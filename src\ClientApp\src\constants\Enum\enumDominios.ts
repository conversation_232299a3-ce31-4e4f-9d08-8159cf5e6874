const emModoProducao = () => {
  return process.env.NODE_ENV && process.env.NODE_ENV === 'production';
};

const baseOptions = [
  {
    value: 'zendar.app',
    label: 'zendar.app',
  },
  {
    value: 'fomer.app',
    label: 'fomer.app',
  },
];

const optionsSomenteEmDev = [
  {
    value: 'fomer.me',
    label: 'fomer.me',
  },
];

export const optionsFomerZendar = [
  ...baseOptions,
  ...(emModoProducao() ? [] : optionsSomenteEmDev),
];

export const optionsPowerStockChef = [
  {
    value: 'powerstock.app',
    label: 'powerstock.app',
  },
  {
    value: 'powerchef.app',
    label: 'powerchef.app',
  },
];

export const DominiosDeliveryEnum = {
  DOMINIO_STI3: 'fomer.delivery',
  DOMINIO_REVENDA: 'oidelivery.app',
} as const;
